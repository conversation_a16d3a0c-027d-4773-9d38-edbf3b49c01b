"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { ArrowLeft, Zap, Star, Trophy, Target, Sparkles, Volume2, VolumeX } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '../../../components/auth/AuthProvider';
import { useRouter, useSearchParams } from 'next/navigation';
import { SentenceTranslation, SentenceSegment, SentenceSegmentOption } from '../../api/games/gem-collector/sentences/route';
import GemCollectorSettings, { GameSettings } from '../../../components/games/GemCollectorSettings';
import confetti from 'canvas-confetti';

interface GemOption {
  id: string;
  text: string;
  isCorrect: boolean;
  lane: number; // 0, 1, or 2 (top, middle, bottom)
  position: number; // x position
  segmentId: string;
  explanation?: string;
  gemType?: 'normal' | 'bonus' | 'power';
  points?: number;
}

interface PowerUp {
  id: string;
  type: 'slow_motion' | 'gem_magnet' | 'double_points' | 'extra_life';
  duration: number;
  active: boolean;
  position: number;
  lane: number;
}

interface ParticleEffect {
  id: string;
  x: number;
  y: number;
  type: 'sparkle' | 'explosion' | 'trail';
  color: string;
  lifetime: number;
}

interface GameSession {
  sessionId: string;
  startTime: Date;
  totalSegments: number;
  correctSegments: number;
  incorrectSegments: number;
  gemsCollected: number;
  speedBoostsUsed: number;
  segmentAttempts: SegmentAttempt[];
}

interface SegmentAttempt {
  segmentId: string;
  selectedOptionId: string;
  isCorrect: boolean;
  responseTime: number;
  gemsEarned: number;
}

interface GameMode {
  type: 'free_play' | 'assignment';
  assignmentId?: string;
  language: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  theme?: string;
  topic?: string;
}

export default function GemCollectorGame() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Game mode and configuration
  const [gameMode, setGameMode] = useState<GameMode>({
    type: 'free_play',
    language: 'spanish',
    difficulty: 'beginner'
  });

  // Game state
  const [sentences, setSentences] = useState<SentenceTranslation[]>([]);
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [playerLane, setPlayerLane] = useState(1); // 0=top, 1=middle, 2=bottom
  const [gems, setGems] = useState<GemOption[]>([]);
  const [powerUps, setPowerUps] = useState<PowerUp[]>([]);
  const [particles, setParticles] = useState<ParticleEffect[]>([]);
  const [score, setScore] = useState(0);
  const [multiplier, setMultiplier] = useState(1);
  const [streak, setStreak] = useState(0);
  const [gameSpeed, setGameSpeed] = useState(2);
  const [speedBoostActive, setSpeedBoostActive] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [feedback, setFeedback] = useState<{ type: 'correct' | 'wrong' | null; text: string }>({ type: null, text: '' });
  const [lives, setLives] = useState(3);
  const [backgroundPosition, setBackgroundPosition] = useState(0);
  const [builtSentence, setBuiltSentence] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(false); // Disabled by default until sound files are added
  const [showTutorial, setShowTutorial] = useState(false);
  const [comboCount, setComboCount] = useState(0);
  const [activePowerUps, setActivePowerUps] = useState<{[key: string]: number}>({});

  // Session tracking
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [segmentStartTime, setSegmentStartTime] = useState<Date | null>(null);

  // Audio refs
  const correctSoundRef = useRef<HTMLAudioElement | null>(null);
  const wrongSoundRef = useRef<HTMLAudioElement | null>(null);
  const gemCollectSoundRef = useRef<HTMLAudioElement | null>(null);
  const powerUpSoundRef = useRef<HTMLAudioElement | null>(null);
  const backgroundMusicRef = useRef<HTMLAudioElement | null>(null);

  // Calculate derived state before any early returns
  const currentSentence = sentences[currentSentenceIndex];
  const currentSegment = currentSentence?.segments[currentSegmentIndex];
  const progressPercentage = sentences.length > 0
    ? ((currentSentenceIndex * 100) / sentences.length) + ((currentSegmentIndex * 100) / (sentences.length * (currentSentence?.segments.length || 1)))
    : 0;

  // Initialize game mode from URL parameters
  useEffect(() => {
    const assignmentId = searchParams?.get('assignment');
    const language = searchParams?.get('language') || 'spanish';
    const difficulty = searchParams?.get('difficulty') || 'beginner';
    const theme = searchParams?.get('theme') || undefined;
    const topic = searchParams?.get('topic') || undefined;

    setGameMode({
      type: assignmentId ? 'assignment' : 'free_play',
      assignmentId: assignmentId || undefined,
      language,
      difficulty: difficulty as 'beginner' | 'intermediate' | 'advanced',
      theme,
      topic
    });
  }, [searchParams]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, isLoading, router]);

  // Fetch sentences when game mode is set
  useEffect(() => {
    if (gameMode && user && !loading) {
      fetchSentences();
    }
  }, [gameMode, user]);

  const fetchSentences = async () => {
    if (loading) return; // Prevent multiple simultaneous requests

    setLoading(true);
    console.log('Fetching sentences with gameMode:', gameMode);
    try {
      const response = await fetch('/api/games/gem-collector/sentences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: gameMode.type === 'assignment' ? 'assignment' : 'freeplay',
          assignmentId: gameMode.assignmentId,
          language: gameMode.language,
          difficulty: gameMode.difficulty,
          theme: gameMode.theme,
          topic: gameMode.topic,
          count: 10
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', response.status, errorData);
        console.log('Using fallback sentences due to API error');
        setSentences(getFallbackSentences());
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('API Response data:', data);

      // Filter out sentences without segments and ensure they have valid segments
      const validSentences = (data.sentences || []).filter((sentence: SentenceTranslation) =>
        sentence.segments && sentence.segments.length > 0 &&
        sentence.segments.every(segment => segment.options && segment.options.length > 0)
      );

      if (validSentences.length === 0) {
        console.log('No valid sentences with segments found, using fallback');
        setSentences(getFallbackSentences());
      } else {
        setSentences(validSentences);
      }
    } catch (error) {
      console.error('Error fetching sentences:', error);
      // Use fallback sentences if API fails
      console.log('Using fallback sentences due to fetch error');
      setSentences(getFallbackSentences());
    } finally {
      setLoading(false);
    }
  };

  // Enhanced sound effect functions using Web Audio API
  const audioContext = useRef<AudioContext | null>(null);

  const initAudioContext = () => {
    if (typeof window !== 'undefined' && !audioContext.current) {
      try {
        audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.error('Audio context creation failed:', error);
      }
    }
  };

  const playTone = (frequency: number, duration: number, type: OscillatorType = 'sine', volume: number = 0.1) => {
    if (!soundEnabled || !audioContext.current) return;

    try {
      const oscillator = audioContext.current.createOscillator();
      const gainNode = audioContext.current.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.current.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContext.current.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0, audioContext.current.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, audioContext.current.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.current.currentTime + duration);

      oscillator.start(audioContext.current.currentTime);
      oscillator.stop(audioContext.current.currentTime + duration);
    } catch (error) {
      console.error('Sound generation error:', error);
    }
  };

  const playCorrectSound = () => {
    if (!soundEnabled) return;
    // Happy ascending chord
    playTone(523.25, 0.15, 'sine', 0.1); // C5
    setTimeout(() => playTone(659.25, 0.15, 'sine', 0.1), 50); // E5
    setTimeout(() => playTone(783.99, 0.2, 'sine', 0.1), 100); // G5
  };

  const playWrongSound = () => {
    if (!soundEnabled) return;
    // Descending sad tone
    playTone(220, 0.3, 'sawtooth', 0.08);
  };

  const playGemCollectSound = () => {
    if (!soundEnabled) return;
    // Quick sparkle sound
    playTone(800, 0.1, 'square', 0.05);
    setTimeout(() => playTone(1200, 0.1, 'square', 0.03), 50);
  };

  const playPowerUpSound = () => {
    if (!soundEnabled) return;
    // Power-up fanfare
    playTone(440, 0.1, 'sine', 0.1);
    setTimeout(() => playTone(554.37, 0.1, 'sine', 0.1), 100);
    setTimeout(() => playTone(659.25, 0.1, 'sine', 0.1), 200);
    setTimeout(() => playTone(880, 0.2, 'sine', 0.1), 300);
  };

  const playSpeedBoostSound = () => {
    if (!soundEnabled) return;
    // Quick whoosh sound
    for (let i = 0; i < 5; i++) {
      setTimeout(() => playTone(200 + i * 100, 0.05, 'sawtooth', 0.03), i * 20);
    }
  };

  // Legacy function for compatibility
  const playSound = (soundRef: React.RefObject<HTMLAudioElement>) => {
    // This is now handled by the specific sound functions above
  };

  // Particle effect functions
  const createParticleEffect = (x: number, y: number, type: 'sparkle' | 'explosion' | 'trail', color: string = '#FFD700') => {
    const newParticles: ParticleEffect[] = [];
    const particleCount = type === 'explosion' ? 8 : 3;

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: `particle-${Date.now()}-${i}`,
        x: x + (Math.random() - 0.5) * 50,
        y: y + (Math.random() - 0.5) * 50,
        type,
        color,
        lifetime: 1000 + Math.random() * 500
      });
    }

    setParticles(prev => [...prev, ...newParticles]);

    // Remove particles after their lifetime
    setTimeout(() => {
      setParticles(prev => prev.filter(p => !newParticles.some(np => np.id === p.id)));
    }, 1500);
  };

  // Enhanced Power-up functions
  const activatePowerUp = (type: PowerUp['type']) => {
    const duration = type === 'extra_life' ? 0 : 5000; // Extra life is instant
    if (duration > 0) {
      setActivePowerUps(prev => ({ ...prev, [type]: Date.now() + duration }));
    }
    playPowerUpSound();

    // Create celebration effect
    if (typeof window !== 'undefined') {
      try {
        confetti({
          particleCount: 30,
          spread: 60,
          origin: { y: 0.7 },
          colors: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1']
        });
      } catch (error) {
        console.error('Confetti error:', error);
      }
    }

    switch (type) {
      case 'slow_motion':
        setGameSpeed(prev => prev * 0.4); // Even slower for better effect
        createParticleEffect(window.innerWidth / 2, window.innerHeight / 2, 'explosion', '#00BFFF');
        break;
      case 'double_points':
        setMultiplier(prev => prev * 2);
        createParticleEffect(window.innerWidth / 2, window.innerHeight / 2, 'explosion', '#FFD700');
        break;
      case 'extra_life':
        setLives(prev => prev + 1);
        createParticleEffect(window.innerWidth / 2, window.innerHeight / 2, 'explosion', '#FF69B4');
        break;
      case 'gem_magnet':
        // Gem magnet will be handled in the game loop
        createParticleEffect(window.innerWidth / 2, window.innerHeight / 2, 'explosion', '#9370DB');
        break;
    }

    if (duration > 0) {
      setTimeout(() => {
        setActivePowerUps(prev => {
          const newState = { ...prev };
          delete newState[type];
          return newState;
        });

        switch (type) {
          case 'slow_motion':
            setGameSpeed(prev => prev / 0.4); // Restore original speed
            break;
          case 'double_points':
            setMultiplier(prev => prev / 2);
            break;
        }
      }, duration);
    }
  };

  // Fallback sentences for when API fails
  const getFallbackSentences = (): SentenceTranslation[] => [
    {
      id: 'fallback-1',
      englishSentence: 'I like to go to the cinema',
      targetLanguage: gameMode.language,
      targetSentence: 'Me gusta ir al cine',
      difficultyLevel: gameMode.difficulty,
      theme: 'Leisure and entertainment',
      topic: 'Free time activities',
      grammarFocus: 'gustar-verb',
      curriculumTier: 'Foundation',
      wordCount: 6,
      complexityScore: 30,
      segments: [
        {
          id: 'fallback-seg-1',
          segmentOrder: 1,
          englishSegment: 'I like',
          targetSegment: 'Me gusta',
          segmentType: 'phrase',
          grammarNote: 'Gustar construction',
          options: [
            { id: 'opt-1', optionText: 'Me gusta', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-2', optionText: 'Me encanta', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-3', optionText: 'Odio', isCorrect: false, distractorType: 'semantic' }
          ]
        },
        {
          id: 'fallback-seg-2',
          segmentOrder: 2,
          englishSegment: 'to go',
          targetSegment: 'ir',
          segmentType: 'word',
          options: [
            { id: 'opt-4', optionText: 'ir', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-5', optionText: 'venir', isCorrect: false, distractorType: 'semantic' },
            { id: 'opt-6', optionText: 'estar', isCorrect: false, distractorType: 'grammatical' }
          ]
        },
        {
          id: 'fallback-seg-3',
          segmentOrder: 3,
          englishSegment: 'to the cinema',
          targetSegment: 'al cine',
          segmentType: 'phrase',
          grammarNote: 'Contraction al = a + el',
          options: [
            { id: 'opt-7', optionText: 'al cine', isCorrect: true, distractorType: 'semantic' },
            { id: 'opt-8', optionText: 'del cine', isCorrect: false, distractorType: 'grammatical' },
            { id: 'opt-9', optionText: 'en cine', isCorrect: false, distractorType: 'grammatical' }
          ]
        }
      ]
    }
  ];

  // Handle keyboard input with improved responsiveness
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!gameStarted || gameOver) return;

    switch (event.key) {
      case 'ArrowUp':
      case 'w':
      case 'W':
        event.preventDefault();
        setPlayerLane(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
      case 's':
      case 'S':
        event.preventDefault();
        setPlayerLane(prev => Math.min(2, prev + 1));
        break;
      case 'ArrowRight':
      case ' ': // Spacebar
      case 'd':
      case 'D':
        event.preventDefault();
        activateSpeedBoost();
        break;
      case 'Escape':
        event.preventDefault();
        setGameOver(true);
        break;
    }
  }, [gameStarted, gameOver]);

  // Handle touch controls for mobile
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (!gameStarted || gameOver) return;

    const touch = event.touches[0];
    const rect = event.currentTarget.getBoundingClientRect();
    const y = touch.clientY - rect.top;
    const height = rect.height;

    // Divide screen into 3 lanes
    const laneHeight = height / 3;
    let targetLane = 1; // Default to middle

    if (y < laneHeight) {
      targetLane = 0; // Top lane
    } else if (y > laneHeight * 2) {
      targetLane = 2; // Bottom lane
    }

    setPlayerLane(targetLane);
  }, [gameStarted, gameOver]);

  // Handle double tap for speed boost on mobile
  const handleDoubleTouch = useCallback(() => {
    if (!gameStarted || gameOver) return;
    activateSpeedBoost();
  }, [gameStarted, gameOver]);

  const activateSpeedBoost = () => {
    if (speedBoostActive) return;

    setSpeedBoostActive(true);
    setGameSpeed(prev => prev * 2);
    playSpeedBoostSound();

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        speedBoostsUsed: prev.speedBoostsUsed + 1
      } : null);
    }

    // Speed boost lasts for 3 seconds
    setTimeout(() => {
      setSpeedBoostActive(false);
      setGameSpeed(prev => prev / 2);
    }, 3000);
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // Game loop
  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = setInterval(() => {
      // Move background
      setBackgroundPosition(prev => prev - gameSpeed);

      // Move gems and check collisions
      setGems(prevGems => {
        let updatedGems = prevGems.map(gem => {
          let newPosition = gem.position - gameSpeed;

          // Gem magnet effect - attract correct gems to player lane
          if (activePowerUps.gem_magnet && gem.isCorrect) {
            const targetLane = playerLane;
            const laneDistance = Math.abs(gem.lane - targetLane);
            if (laneDistance > 0 && gem.position < 400) {
              // Gradually move gem towards player lane
              const direction = gem.lane < targetLane ? 0.1 : -0.1;
              return {
                ...gem,
                position: newPosition,
                lane: Math.max(0, Math.min(2, gem.lane + direction))
              };
            }
          }

          return {
            ...gem,
            position: newPosition
          };
        }).filter(gem => gem.position > -100);

        // Improved collision detection with better hit box
        const playerGem = updatedGems.find(gem => {
          const isInLane = Math.abs(gem.lane - playerLane) < 0.3; // Allow for floating point lane positions
          const isInRange = gem.position >= 180 && gem.position <= 320; // Wider collision area
          return isInLane && isInRange;
        });

        if (playerGem) {
          handleGemCollection(playerGem);
          return updatedGems.filter(g => g.id !== playerGem.id);
        }

        return updatedGems;
      });

      // Move power-ups and check collisions
      setPowerUps(prevPowerUps => {
        const updatedPowerUps = prevPowerUps.map(powerUp => ({
          ...powerUp,
          position: powerUp.position - gameSpeed
        })).filter(powerUp => powerUp.position > -100);

        // Improved collision detection for power-ups
        const playerPowerUp = updatedPowerUps.find(powerUp => {
          const isInLane = powerUp.lane === playerLane;
          const isInRange = powerUp.position >= 180 && powerUp.position <= 320;
          return isInLane && isInRange;
        });

        if (playerPowerUp) {
          activatePowerUp(playerPowerUp.type);
          return updatedPowerUps.filter(p => p.id !== playerPowerUp.id);
        }

        return updatedPowerUps;
      });
    }, 50);

    return () => clearInterval(gameLoop);
  }, [gameStarted, gameOver, playerLane, gameSpeed, currentSentenceIndex, currentSegmentIndex, streak]);

  const handleGemCollection = (gem: GemOption) => {
    const responseTime = segmentStartTime ? Date.now() - segmentStartTime.getTime() : 0;
    const gemElement = document.querySelector(`[data-gem-id="${gem.id}"]`);
    const gemRect = gemElement?.getBoundingClientRect();

    // Create particle effect at gem position
    if (gemRect) {
      createParticleEffect(
        gemRect.left + gemRect.width / 2,
        gemRect.top + gemRect.height / 2,
        gem.isCorrect ? 'explosion' : 'sparkle',
        gem.isCorrect ? '#00FF88' : '#FF4444'
      );
    }

    // Record the attempt
    const basePoints = gem.isCorrect ? (gem.points || 100) : 0;
    const streakBonus = gem.isCorrect ? streak * 10 : 0;
    const multiplierBonus = basePoints * (multiplier - 1);
    const totalPoints = Math.round(basePoints + streakBonus + multiplierBonus);

    const attempt: SegmentAttempt = {
      segmentId: gem.segmentId,
      selectedOptionId: gem.id,
      isCorrect: gem.isCorrect,
      responseTime,
      gemsEarned: gem.isCorrect ? 10 : 0
    };

    // Update session tracking
    if (gameSession) {
      setGameSession(prev => prev ? {
        ...prev,
        totalSegments: prev.totalSegments + 1,
        correctSegments: prev.correctSegments + (gem.isCorrect ? 1 : 0),
        incorrectSegments: prev.incorrectSegments + (gem.isCorrect ? 0 : 1),
        gemsCollected: prev.gemsCollected + attempt.gemsEarned,
        segmentAttempts: [...prev.segmentAttempts, attempt]
      } : null);
    }

    if (gem.isCorrect) {
      // Update streak and combo
      setStreak(prev => prev + 1);
      setComboCount(prev => prev + 1);

      // Play success sound and confetti
      playCorrectSound();
      playGemCollectSound();
      if (streak > 2 && typeof window !== 'undefined') {
        try {
          confetti({
            particleCount: 50,
            spread: 70,
            origin: { y: 0.6 }
          });
        } catch (error) {
          console.error('Confetti error:', error);
        }
      }

      // Add to built sentence
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (currentSegment) {
        setBuiltSentence(prev => [...prev, currentSegment.targetSegment]);
        setScore(prev => prev + totalPoints);

        let feedbackText = `Correct! "${currentSegment.targetSegment}"`;
        if (streak > 1) feedbackText += ` 🔥${streak} streak!`;
        if (multiplier > 1) feedbackText += ` ✨${multiplier}x multiplier!`;
        feedbackText += ` +${totalPoints} points`;

        setFeedback({
          type: 'correct',
          text: feedbackText
        });

        // Move to next segment or sentence
        setTimeout(() => {
          moveToNextSegment();
          setFeedback({ type: null, text: '' });
        }, 1500);
      }
    } else {
      // Reset streak on wrong answer
      setStreak(0);
      setComboCount(0);

      // Play error sound
      playWrongSound();

      setLives(prev => {
        const newLives = prev - 1;
        if (newLives <= 0) {
          setGameOver(true);
        }
        return newLives;
      });
      setFeedback({
        type: 'wrong',
        text: gem.explanation || 'Wrong! Try again'
      });
      setTimeout(() => setFeedback({ type: null, text: '' }), 2000);
    }
  };

  const moveToNextSegment = () => {
    const currentSentence = sentences[currentSentenceIndex];
    if (!currentSentence) return;

    if (currentSegmentIndex < currentSentence.segments.length - 1) {
      // Move to next segment in current sentence
      setCurrentSegmentIndex(prev => prev + 1);
      setSegmentStartTime(new Date());
    } else {
      // Sentence completed, move to next sentence
      if (currentSentenceIndex < sentences.length - 1) {
        setCurrentSentenceIndex(prev => prev + 1);
        setCurrentSegmentIndex(0);
        setBuiltSentence([]);
        setGameSpeed(prev => prev + 0.1); // Gradually increase difficulty
        setSegmentStartTime(new Date());
      } else {
        // All sentences completed
        setGameOver(true);
      }
    }
  };

  // Generate gems for current segment
  useEffect(() => {
    if (!gameStarted || gameOver || sentences.length === 0) return;

    const generateGems = () => {
      const currentSentence = sentences[currentSentenceIndex];
      const currentSegment = currentSentence?.segments[currentSegmentIndex];

      if (!currentSegment || currentSegment.options.length === 0) return;

      // Shuffle options and take up to 3
      const shuffledOptions = [...currentSegment.options].sort(() => Math.random() - 0.5);
      const selectedOptions = shuffledOptions.slice(0, 3);

      // Ensure we have exactly one correct option
      const hasCorrect = selectedOptions.some(opt => opt.isCorrect);
      if (!hasCorrect) {
        const correctOption = currentSegment.options.find(opt => opt.isCorrect);
        if (correctOption) {
          selectedOptions[Math.floor(Math.random() * selectedOptions.length)] = correctOption;
        }
      }

      const spawnX = typeof window !== 'undefined' ? window.innerWidth + 100 : 800;

      const newGems: GemOption[] = selectedOptions.map((option, index) => {
        // Determine gem type and points based on correctness and streak
        let gemType: 'normal' | 'bonus' | 'power' = 'normal';
        let points = 100;

        if (option.isCorrect) {
          if (streak > 5) {
            gemType = 'power';
            points = 200;
          } else if (streak > 2) {
            gemType = 'bonus';
            points = 150;
          }
        }

        return {
          id: option.id,
          text: option.optionText,
          isCorrect: option.isCorrect,
          lane: index,
          position: spawnX + (index * 120), // stagger slightly
          segmentId: currentSegment.id,
          explanation: option.explanation,
          gemType,
          points
        };
      });

      setGems(prev => [...prev, ...newGems]);

      // Enhanced power-up spawning logic
      const shouldSpawnPowerUp = Math.random() < (streak > 10 ? 0.25 : streak > 5 ? 0.18 : 0.12);
      if (shouldSpawnPowerUp && streak > 2) {
        // Weight power-ups based on game state
        const powerUpWeights = {
          'slow_motion': lives < 2 ? 0.3 : 0.2, // More likely when low on lives
          'gem_magnet': 0.25,
          'double_points': score < 1000 ? 0.3 : 0.2, // More likely early in game
          'extra_life': lives < 3 ? 0.25 : 0.1 // Much more likely when low on lives
        };

        // Select weighted random power-up
        const totalWeight = Object.values(powerUpWeights).reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;
        let selectedType: PowerUp['type'] = 'gem_magnet';

        for (const [type, weight] of Object.entries(powerUpWeights)) {
          random -= weight;
          if (random <= 0) {
            selectedType = type as PowerUp['type'];
            break;
          }
        }

        const newPowerUp: PowerUp = {
          id: `powerup-${Date.now()}`,
          type: selectedType,
          duration: 5000,
          active: false,
          position: spawnX + 250,
          lane: Math.floor(Math.random() * 3)
        };

        setPowerUps(prev => [...prev, newPowerUp]);
      }
    };

    // Generate initial gems
    generateGems();

    // Generate new gems periodically
    const gemGenerator = setInterval(generateGems, 4000);

    return () => clearInterval(gemGenerator);
  }, [currentSentenceIndex, currentSegmentIndex, gameStarted, gameOver, sentences, streak]);

  // Initialize game session when starting
  const initializeGameSession = () => {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const session: GameSession = {
      sessionId,
      startTime: new Date(),
      totalSegments: 0,
      correctSegments: 0,
      incorrectSegments: 0,
      gemsCollected: 0,
      speedBoostsUsed: 0,
      segmentAttempts: []
    };

    setGameSession(session);
    setSegmentStartTime(new Date());
  };

  const startGame = (customSettings?: GameSettings) => {
    if (gameMode.type === 'free_play' && !customSettings) {
      setShowSettings(true);
      return;
    }

    if (customSettings) {
      setGameMode(prev => ({
        ...prev,
        language: customSettings.language,
        difficulty: customSettings.difficulty,
        theme: customSettings.theme,
        topic: customSettings.topic
      }));
      setLives(customSettings.livesCount);
    }

    if (sentences.length === 0) {
      alert('No sentences available. Please try again later.');
      return;
    }

    // Reset all game state
    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setMultiplier(1);
    setStreak(0);
    setComboCount(0);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setPowerUps([]);
    setParticles([]);
    setActivePowerUps({});
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    initializeGameSession();
  };

  const resetGame = () => {
    setGameStarted(false);
    setGameOver(false);
    setScore(0);
    setMultiplier(1);
    setStreak(0);
    setComboCount(0);
    setLives(3);
    setCurrentSentenceIndex(0);
    setCurrentSegmentIndex(0);
    setPlayerLane(1);
    setGems([]);
    setPowerUps([]);
    setParticles([]);
    setActivePowerUps({});
    setGameSpeed(2);
    setSpeedBoostActive(false);
    setBuiltSentence([]);
    setFeedback({ type: null, text: '' });
    setGameSession(null);
    setSegmentStartTime(null);
  };

  // Save game session when game ends
  const saveGameSession = async () => {
    if (!gameSession || !user) return;

    try {
      const response = await fetch('/api/games/gem-collector/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: gameSession.sessionId,
          assignmentId: gameMode.assignmentId,
          sessionType: gameMode.type,
          languagePair: `english_${gameMode.language}`,
          difficultyLevel: gameMode.difficulty,
          totalSentences: sentences.length,
          completedSentences: currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0),
          totalSegments: gameSession.totalSegments,
          correctSegments: gameSession.correctSegments,
          incorrectSegments: gameSession.incorrectSegments,
          finalScore: score,
          gemsCollected: gameSession.gemsCollected,
          speedBoostsUsed: gameSession.speedBoostsUsed,
          segmentAttempts: gameSession.segmentAttempts,
          endedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        console.error('Failed to save game session');
      }
    } catch (error) {
      console.error('Error saving game session:', error);
    }
  };

  // Save session when game ends
  useEffect(() => {
    if (gameOver && gameSession) {
      saveGameSession();
    }
  }, [gameOver, gameSession]);

  // Initialize sentences immediately on mount
  useEffect(() => {
    if (sentences.length === 0) {
      setSentences(getFallbackSentences());
    }
  }, []); // Empty dependency array to run only once on mount

  // Initialize audio context safely
  useEffect(() => {
    if (typeof window !== 'undefined' && soundEnabled) {
      initAudioContext();
    }
  }, [soundEnabled]);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const handleFirstInteraction = () => {
      if (soundEnabled) {
        initAudioContext();
        if (audioContext.current?.state === 'suspended') {
          audioContext.current.resume();
        }
      }
    };

    document.addEventListener('click', handleFirstInteraction, { once: true });
    document.addEventListener('keydown', handleFirstInteraction, { once: true });
    document.addEventListener('touchstart', handleFirstInteraction, { once: true });

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };
  }, [soundEnabled]);

  // --------------------------
  //   Render Conditions
  // --------------------------

  // Show loading spinner while authenticating or fetching sentences
  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white font-medium">
            {isLoading ? 'Loading game...' : 'Preparing sentences...'}
          </p>
        </div>
      </div>
    );
  }

  // If the user is not authenticated (a redirect will trigger shortly)
  if (!user) {
    return null;
  }

  // Error boundary for development
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    try {
      // Wrap the main game logic in a try-catch for debugging
    } catch (error) {
      console.error('Game render error:', error);
      return (
        <div className="min-h-screen bg-red-900 flex items-center justify-center text-white">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Game Error</h1>
            <p>Please check the console for details.</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
            >
              Reload Game
            </button>
          </div>
        </div>
      );
    }
  }

  // Tutorial Component
  const TutorialModal = () => (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-white rounded-3xl p-8 max-w-2xl w-full mx-4 text-center shadow-2xl">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <span className="text-2xl">🎓</span>
        </div>

        <h2 className="text-3xl font-bold text-slate-800 mb-6">How to Play Gem Collector</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-blue-50 rounded-xl p-4">
            <div className="text-2xl mb-2">🎯</div>
            <h3 className="font-bold text-slate-800 mb-2">Collect Correct Words</h3>
            <p className="text-slate-600 text-sm">Move between lanes to collect the correct translation segments in order.</p>
          </div>

          <div className="bg-green-50 rounded-xl p-4">
            <div className="text-2xl mb-2">⌨️</div>
            <h3 className="font-bold text-slate-800 mb-2">Controls</h3>
            <p className="text-slate-600 text-sm">Use ↑↓ arrow keys or W/S to move. Space or → for speed boost.</p>
          </div>

          <div className="bg-purple-50 rounded-xl p-4">
            <div className="text-2xl mb-2">📱</div>
            <h3 className="font-bold text-slate-800 mb-2">Mobile Controls</h3>
            <p className="text-slate-600 text-sm">Tap different areas of the screen to move lanes. Double-tap for speed boost.</p>
          </div>

          <div className="bg-yellow-50 rounded-xl p-4">
            <div className="text-2xl mb-2">⚡</div>
            <h3 className="font-bold text-slate-800 mb-2">Power-ups</h3>
            <p className="text-slate-600 text-sm">Collect special power-ups for slow motion, extra points, and more!</p>
          </div>
        </div>

        <div className="flex gap-4">
          <button
            onClick={() => setShowTutorial(false)}
            className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-2xl px-6 py-3 hover:scale-105 transition-all"
          >
            Got it! Let's Play! 🚀
          </button>
        </div>
      </div>
    </div>
  );

  if (!gameStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 overflow-hidden relative">
        {showTutorial && <TutorialModal />}
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500/20 rounded-full animate-bounce"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500/20 rounded-full animate-ping"></div>
          <div className="absolute bottom-40 right-1/3 w-14 h-14 bg-cyan-500/20 rounded-full animate-pulse"></div>
        </div>

        <div className="flex items-center justify-center min-h-screen">
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-8 max-w-lg w-full mx-4 text-center shadow-2xl border border-white/20 animate-scale-in">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-6 animate-bounce">
              <span className="text-2xl">💎</span>
            </div>

            <h1 className="text-3xl font-bold text-slate-800 mb-4">
              ✨ Gem Collector ✨
              {gameMode.type === 'assignment' && (
                <span className="block text-lg text-blue-600 font-normal mt-1">📚 Assignment Mode</span>
              )}
            </h1>

            <p className="text-slate-600 mb-6 leading-relaxed">
              🎯 Build complete sentence translations word-by-word! Collect the correct translation gems
              in order to build each sentence. Use speed boost for extra challenge!
            </p>

            <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-4 mb-6 border border-slate-200">
              <h3 className="font-semibold text-slate-800 mb-3">🎮 How to Play:</h3>
              <ul className="text-sm text-slate-600 space-y-2 text-left">
                <li>• ⬆️ ⬇️ Use arrow keys to move between lanes</li>
                <li>• 🎯 Collect gems with correct translation segments in order</li>
                <li>• ⚡ Press → (right arrow) to activate speed boost</li>
                <li>• 🏆 Build complete sentences to earn bonus points</li>
                <li>• 💖 You have 3 lives - avoid wrong gems!</li>
              </ul>
            </div>

            {sentences.length > 0 && (
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6 border border-blue-200">
                <h3 className="font-semibold text-blue-800 mb-2">🚀 Ready to Start:</h3>
                <div className="text-sm text-blue-600">
                  <p>📚 {sentences.length} sentences loaded</p>
                  <p>🌍 Language: {gameMode.language.charAt(0).toUpperCase() + gameMode.language.slice(1)}</p>
                  <p>📊 Difficulty: {gameMode.difficulty.charAt(0).toUpperCase() + gameMode.difficulty.slice(1)}</p>
                  {gameMode.theme && <p>🎯 Theme: {gameMode.theme}</p>}
                  {gameMode.topic && <p>📖 Topic: {gameMode.topic}</p>}
                </div>
              </div>
            )}

            <div className="space-y-4">
              <button
                onClick={() => startGame()}
                disabled={sentences.length === 0}
                className="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-bold rounded-2xl px-8 py-4 text-lg shadow-xl hover:shadow-2xl transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {sentences.length === 0 ? '⏳ Loading Sentences...' :
                 gameMode.type === 'assignment' ? '🎓 Start Assignment! 💎' : '⚙️ Configure & Start! 🎮'}
              </button>

              <button
                onClick={() => setShowTutorial(true)}
                className="w-full bg-gradient-to-r from-slate-200 to-gray-300 text-slate-700 font-bold rounded-2xl px-8 py-3 text-base hover:from-slate-300 hover:to-gray-400 transition-all transform hover:scale-105"
              >
                📚 How to Play
              </button>
            </div>

            {/* Settings Modal (Configure & Start) */}
            <GemCollectorSettings
              isOpen={showSettings}
              onClose={() => setShowSettings(false)}
              onStartGame={(settings) => {
                const newGameMode = {
                  ...gameMode,
                  language: settings.language,
                  difficulty: settings.difficulty,
                  theme: settings.theme,
                  topic: settings.topic
                };
                setGameMode(newGameMode);

                // Fetch sentences with new settings, then start the game
                fetchSentences().then(() => {
                  startGame(settings);
                });
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 overflow-hidden relative">
      {/* Audio Elements - Hidden for now until sound files are added */}
      {soundEnabled && (
        <>
          <audio ref={correctSoundRef} preload="none" style={{ display: 'none' }}>
            {/* Sound files would go here when available */}
          </audio>
          <audio ref={wrongSoundRef} preload="none" style={{ display: 'none' }}>
            {/* Sound files would go here when available */}
          </audio>
          <audio ref={gemCollectSoundRef} preload="none" style={{ display: 'none' }}>
            {/* Sound files would go here when available */}
          </audio>
          <audio ref={powerUpSoundRef} preload="none" style={{ display: 'none' }}>
            {/* Sound files would go here when available */}
          </audio>
          <audio ref={backgroundMusicRef} loop preload="none" style={{ display: 'none' }}>
            {/* Sound files would go here when available */}
          </audio>
        </>
      )}

      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500/20 rounded-full animate-ping"></div>
        <div className="absolute bottom-40 right-1/3 w-14 h-14 bg-cyan-500/20 rounded-full animate-pulse"></div>

        {/* Floating stars */}
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/40 rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Enhanced Header */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-black/30 backdrop-blur-lg border-b border-white/10">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/games" className="text-white hover:text-cyan-300 transition-all duration-300 hover:scale-110">
              <ArrowLeft className="w-6 h-6 drop-shadow-lg" />
            </Link>
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className="text-white hover:text-cyan-300 transition-all duration-300 hover:scale-110"
            >
              {soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
            </button>
          </div>

          <div className="flex items-center gap-4 text-white text-sm">
            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
              <div className="text-xs opacity-80">Score</div>
              <div className="text-xl font-bold text-cyan-300">{score.toLocaleString()}</div>
            </div>

            {multiplier > 1 && (
              <div className="text-center bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-lg px-3 py-2 border border-yellow-400/30 animate-pulse">
                <div className="text-xs opacity-80">Multiplier</div>
                <div className="text-lg font-bold text-yellow-300">{multiplier}x</div>
              </div>
            )}

            {streak > 1 && (
              <div className="text-center bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg px-3 py-2 border border-red-400/30">
                <div className="text-xs opacity-80">Streak</div>
                <div className="text-lg font-bold text-red-300 flex items-center gap-1">
                  🔥 {streak}
                </div>
              </div>
            )}

            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
              <div className="text-xs opacity-80">Lives</div>
              <div className="text-lg font-bold flex items-center gap-1">
                {Array.from({ length: lives }).map((_, i) => (
                  <span key={i} className="text-red-400 animate-pulse">💖</span>
                ))}
              </div>
            </div>

            <div className="text-center bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
              <div className="text-xs opacity-80">Progress</div>
              <div className="text-lg font-bold text-green-300">{currentSentenceIndex + 1}/{sentences.length}</div>
            </div>

            {/* Active Power-ups Display */}
            {Object.keys(activePowerUps).length > 0 && (
              <div className="flex gap-2">
                {Object.entries(activePowerUps).map(([type, endTime]) => (
                  <div key={type} className="text-center bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg px-2 py-1 border border-purple-400/30 animate-pulse">
                    <div className="text-xs font-bold text-purple-300">
                      {type === 'slow_motion' && '🐌'}
                      {type === 'double_points' && '⭐'}
                      {type === 'gem_magnet' && '🧲'}
                      {type === 'extra_life' && '💖'}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {speedBoostActive && (
              <div className="text-center bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-lg px-3 py-2 animate-pulse border border-yellow-400/30">
                <div className="text-xs opacity-80">Speed Boost</div>
                <div className="text-lg font-bold flex items-center gap-1">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 animate-bounce">ACTIVE</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Progress Bar */}
      <div className="absolute top-20 left-0 right-0 z-20">
        <div className="container mx-auto px-4">
          <div className="bg-white/10 rounded-full h-4 shadow-lg border border-white/20">
            <div
              className={`h-4 rounded-full transition-all duration-500 shadow-lg relative overflow-hidden ${
                progressPercentage > 80 ? 'bg-gradient-to-r from-green-400 via-emerald-500 to-green-600 animate-pulse' :
                progressPercentage > 50 ? 'bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-600' :
                'bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500'
              }`}
              style={{ width: `${Math.min(100, progressPercentage)}%` }}
            >
              {/* Animated shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
            </div>
          </div>

          {/* Progress text */}
          <div className="text-center mt-2">
            <span className="text-white text-sm font-medium bg-black/30 px-3 py-1 rounded-full backdrop-blur-sm">
              Sentence {currentSentenceIndex + 1} of {sentences.length}
              {currentSegment && ` • Segment ${currentSegmentIndex + 1} of ${currentSentence?.segments.length || 0}`}
            </span>
          </div>
        </div>
      </div>

      {/* Current Sentence Building */}
      <div className="absolute top-28 left-0 right-0 z-20 text-center">
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-6 mx-4 shadow-2xl border border-white/20">
          {currentSentence && (
            <>
              <div className="text-lg text-gradient bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold mb-3">
                🌍 Translate to {currentSentence.targetLanguage.charAt(0).toUpperCase() + currentSentence.targetLanguage.slice(1)}
              </div>
              <div className="text-2xl font-bold text-slate-800 mb-4 leading-relaxed">
                "{currentSentence.englishSentence}"
              </div>

              {/* Built sentence so far */}
              <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl p-4 mb-4 border border-slate-200">
                <div className="text-sm text-slate-600 mb-2 font-medium">🔨 Building Translation:</div>
                <div className="text-xl font-bold text-slate-800 min-h-[32px] leading-relaxed">
                  {builtSentence.length > 0 ? (
                    <span className="animate-fade-in">{builtSentence.join(' ')}</span>
                  ) : (
                    <span className="text-slate-400 italic">Choose the correct words...</span>
                  )}
                  {currentSegment && (
                    <span className="text-blue-600 ml-2 font-semibold animate-pulse">
                      + "{currentSegment.englishSegment}"
                    </span>
                  )}
                </div>
              </div>

              {/* Current segment hint */}
              {currentSegment && (
                <div className="text-base text-slate-700 bg-blue-50 rounded-xl p-3">
                  <span className="font-bold text-blue-700">🎯 Next:</span> "{currentSegment.englishSegment}"
                  {currentSegment.grammarNote && (
                    <div className="text-sm text-blue-600 mt-2 font-medium">
                      💡 {currentSegment.grammarNote}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Game Area */}
      <div
        className="absolute inset-0 pt-64"
        onTouchStart={handleTouchStart}
        onDoubleClick={handleDoubleTouch}
      >
        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-white/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>

        {/* Magical Selection Zones */}
        <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-80">
          <div className="h-full relative mx-8">
            {[0, 1, 2].map((lane) => (
              <div
                key={lane}
                className={`absolute left-0 right-0 h-1/3 border-2 border-dashed transition-all duration-300 rounded-2xl ${
                  playerLane === lane 
                    ? 'border-cyan-400 bg-cyan-500/10 shadow-lg shadow-cyan-500/20' 
                    : 'border-white/20 bg-white/5'
                }`}
                style={{ top: `${lane * 33.33}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-2xl"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced Player Character */}
        <div
          className="absolute left-16 transition-all duration-300 z-10"
          style={{
            top: `calc(50% - 120px + ${playerLane * 107}px)`,
          }}
        >
          <div className="relative">
            {/* Power-up aura effects */}
            {activePowerUps.slow_motion && (
              <div className="absolute inset-0 w-20 h-20 -m-2 bg-blue-400/30 rounded-full animate-pulse"></div>
            )}
            {activePowerUps.double_points && (
              <div className="absolute inset-0 w-20 h-20 -m-2 bg-yellow-400/30 rounded-full animate-ping"></div>
            )}
            {activePowerUps.gem_magnet && (
              <div className="absolute inset-0 w-24 h-24 -m-4 bg-purple-400/20 rounded-full animate-spin"></div>
            )}

            <div className={`w-16 h-16 rounded-full flex items-center justify-center shadow-2xl transition-all duration-300 ${
              Object.keys(activePowerUps).length > 0
                ? 'bg-gradient-to-br from-purple-400 via-pink-500 to-yellow-500 animate-pulse scale-110'
                : 'bg-gradient-to-br from-yellow-400 to-orange-500 animate-bounce'
            }`}>
              <span className="text-2xl animate-pulse">
                {activePowerUps.gem_magnet ? '🧲' :
                 activePowerUps.slow_motion ? '🐌' :
                 activePowerUps.double_points ? '⭐' : '✨'}
              </span>
            </div>

            {/* Enhanced ping effect */}
            <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full ${
              Object.keys(activePowerUps).length > 0
                ? 'bg-gradient-to-r from-purple-400 to-pink-500 animate-ping'
                : 'bg-gradient-to-r from-pink-400 to-purple-500 animate-ping'
            }`}></div>

            {/* Speed boost trail effect */}
            {speedBoostActive && (
              <div className="absolute inset-0 w-16 h-16">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-full h-full bg-yellow-400/20 rounded-full animate-ping"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Floating Word Options */}
        {gems.map((gem) => (
          <div
            key={gem.id}
            data-gem-id={gem.id}
            className="absolute transition-all duration-200 z-10 animate-float-in"
            style={{
              left: `${gem.position}px`,
              top: `calc(50% - 120px + ${gem.lane * 107}px)`,
            }}
          >
            <div className={`relative px-6 py-3 rounded-2xl font-bold shadow-2xl hover:scale-110 transition-all duration-300 text-base whitespace-nowrap cursor-pointer ${
              gem.isCorrect
                ? gem.gemType === 'power'
                  ? 'bg-gradient-to-r from-purple-400 via-pink-500 to-purple-600 text-white border-2 border-purple-300 shadow-purple-500/50 animate-pulse'
                  : gem.gemType === 'bonus'
                  ? 'bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-600 text-white border-2 border-yellow-300 shadow-yellow-500/50'
                  : 'bg-gradient-to-r from-emerald-400 via-green-500 to-emerald-600 text-white border-2 border-emerald-300 shadow-emerald-500/50'
                : 'bg-gradient-to-r from-slate-400 via-gray-500 to-slate-600 text-white border-2 border-slate-300 shadow-slate-500/50'
            }`}>
              {gem.text}

              {/* Gem type indicators */}
              {gem.isCorrect && gem.gemType === 'power' && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-xs animate-spin">
                  ⚡
                </div>
              )}
              {gem.isCorrect && gem.gemType === 'bonus' && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-xs animate-bounce">
                  ⭐
                </div>
              )}

              {/* Points indicator */}
              {gem.isCorrect && gem.points && gem.points > 100 && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-white/90 text-black text-xs px-2 py-1 rounded-full font-bold">
                  +{gem.points}
                </div>
              )}

              <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>

              {/* Sparkle effect for special gems */}
              {gem.isCorrect && gem.gemType !== 'normal' && (
                <div className="absolute inset-0 rounded-2xl overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Power-ups */}
        {powerUps.map((powerUp) => (
          <div
            key={powerUp.id}
            className="absolute transition-all duration-200 z-10 animate-float-in"
            style={{
              left: `${powerUp.position}px`,
              top: `calc(50% - 120px + ${powerUp.lane * 107}px)`,
            }}
          >
            <div className="relative w-16 h-16 bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl border-4 border-white/30 animate-pulse hover:scale-110 transition-all duration-300">
              <span className="text-2xl animate-bounce">
                {powerUp.type === 'slow_motion' && '🐌'}
                {powerUp.type === 'gem_magnet' && '🧲'}
                {powerUp.type === 'double_points' && '⭐'}
                {powerUp.type === 'extra_life' && '💖'}
              </span>

              {/* Rotating ring effect */}
              <div className="absolute inset-0 rounded-full border-2 border-dashed border-white/50 animate-spin"></div>

              {/* Pulsing glow */}
              <div className="absolute inset-0 rounded-full bg-purple-400/30 animate-ping"></div>
            </div>
          </div>
        ))}

        {/* Particle Effects */}
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute pointer-events-none z-20"
            style={{
              left: `${particle.x}px`,
              top: `${particle.y}px`,
              color: particle.color,
            }}
          >
            <div className={`text-2xl animate-bounce ${
              particle.type === 'explosion' ? 'animate-ping' : 'animate-pulse'
            }`}>
              {particle.type === 'sparkle' && '✨'}
              {particle.type === 'explosion' && '💥'}
              {particle.type === 'trail' && '⭐'}
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Feedback with Animation */}
      {feedback.type && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 animate-scale-in">
          <div className={`
            px-10 py-6 rounded-3xl font-bold text-3xl shadow-2xl border-4 transform transition-all backdrop-blur-lg
            ${feedback.type === 'correct'
              ? 'bg-gradient-to-r from-green-400 via-emerald-500 to-green-600 border-green-200 text-white shadow-green-500/50'
              : 'bg-gradient-to-r from-red-400 via-rose-500 to-red-600 border-red-200 text-white shadow-red-500/50'
            }
          `}>
            <div className="absolute inset-0 bg-white/10 rounded-3xl animate-pulse"></div>
            <div className="flex items-center justify-center">
              <span className="mr-3 text-4xl">
                {feedback.type === 'correct' ? '🎉' : '💫'}
              </span>
              {feedback.text}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Combo/Streak Indicator */}
      {streak > 2 && (
        <div className="absolute top-36 right-8 z-20 animate-bounce-in">
          <div className={`text-white px-6 py-3 rounded-2xl shadow-2xl border-2 transition-all duration-300 ${
            streak >= 10 ? 'bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 border-purple-300 animate-pulse' :
            streak >= 5 ? 'bg-gradient-to-r from-red-400 via-orange-500 to-yellow-500 border-red-300' :
            'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 border-yellow-300'
          }`}>
            <div className="text-sm font-bold flex items-center gap-2">
              🔥 {streak} Streak!
              {streak >= 10 && <span className="animate-spin">⭐</span>}
              {streak >= 5 && streak < 10 && <span className="animate-bounce">🚀</span>}
            </div>
            {multiplier > 1 && (
              <div className="text-xs opacity-90 mt-1">
                {multiplier}x Multiplier Active!
              </div>
            )}
          </div>
        </div>
      )}

      {/* Achievement Notifications */}
      {streak === 5 && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 animate-scale-in">
          <div className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-8 py-4 rounded-3xl shadow-2xl border-4 border-yellow-300">
            <div className="text-center">
              <div className="text-3xl mb-2">🏆</div>
              <div className="text-xl font-bold">STREAK MASTER!</div>
              <div className="text-sm opacity-90">5 correct answers in a row!</div>
            </div>
          </div>
        </div>
      )}

      {streak === 10 && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 animate-scale-in">
          <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-white px-8 py-4 rounded-3xl shadow-2xl border-4 border-purple-300 animate-pulse">
            <div className="text-center">
              <div className="text-3xl mb-2">👑</div>
              <div className="text-xl font-bold">LEGENDARY STREAK!</div>
              <div className="text-sm opacity-90">10 perfect answers! You're unstoppable!</div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Game Over */}
      {gameOver && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-40">
          <div className="bg-white rounded-3xl p-10 max-w-2xl w-full mx-4 text-center shadow-2xl border border-gray-200 animate-scale-in">
            {/* Celebration confetti */}
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 text-6xl animate-bounce">🎊</div>
            <div className="absolute top-8 left-8 text-4xl animate-pulse">🌟</div>
            <div className="absolute top-8 right-8 text-4xl animate-pulse">✨</div>

            <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-2xl animate-bounce">
              <span className="text-3xl">
                {currentSentenceIndex >= sentences.length - 1 ? '🏆' : lives <= 0 ? '💔' : '🎯'}
              </span>
            </div>

            <h2 className="text-3xl font-bold text-slate-800 mb-4">
              {currentSentenceIndex >= sentences.length - 1 ? 'Perfect! All Sentences Complete!' :
               lives <= 0 ? 'Game Over!' : 'Great Effort!'}
            </h2>

            {/* Performance rating */}
            {gameSession && (
              <div className="mb-6">
                {(() => {
                  const accuracy = Math.round((gameSession.correctSegments / Math.max(1, gameSession.totalSegments)) * 100);
                  if (accuracy >= 90) return <div className="text-2xl text-green-600 font-bold">🌟 EXCELLENT! 🌟</div>;
                  if (accuracy >= 75) return <div className="text-2xl text-blue-600 font-bold">👍 GREAT JOB! 👍</div>;
                  if (accuracy >= 60) return <div className="text-2xl text-yellow-600 font-bold">👌 GOOD WORK! 👌</div>;
                  return <div className="text-2xl text-orange-600 font-bold">💪 KEEP PRACTICING! 💪</div>;
                })()}
              </div>
            )}

            <div className="bg-slate-50 rounded-xl p-4 mb-6">
              <div className="text-2xl font-bold text-blue-600 mb-2">Final Score</div>
              <div className="text-4xl font-bold text-slate-800 mb-4">{score.toLocaleString()}</div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center bg-white rounded-lg p-3">
                  <div className="text-slate-600">Sentences</div>
                  <div className="font-bold text-slate-800 text-lg">
                    {currentSentenceIndex + (currentSegmentIndex > 0 ? 1 : 0)} / {sentences.length}
                  </div>
                </div>
                <div className="text-center bg-white rounded-lg p-3">
                  <div className="text-slate-600">Accuracy</div>
                  <div className="font-bold text-slate-800 text-lg">
                    {gameSession ? Math.round((gameSession.correctSegments / Math.max(1, gameSession.totalSegments)) * 100) : 0}%
                  </div>
                </div>
                <div className="text-center bg-white rounded-lg p-3">
                  <div className="text-slate-600">Best Streak</div>
                  <div className="font-bold text-slate-800 text-lg">{streak}</div>
                </div>
                <div className="text-center bg-white rounded-lg p-3">
                  <div className="text-slate-600">Power-ups Used</div>
                  <div className="font-bold text-slate-800 text-lg">{Object.keys(activePowerUps).length}</div>
                </div>
              </div>
            </div>

            {gameMode.type === 'assignment' && (
              <div className="bg-blue-50 rounded-xl p-4 mb-6 border border-blue-200">
                <div className="text-base text-blue-800 font-medium">
                  📚 Assignment completed! Your progress has been saved.
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={resetGame}
                className="flex-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-bold rounded-2xl px-8 py-4 shadow-xl hover:shadow-2xl transform transition-all hover:scale-105 text-lg"
              >
                🎮 Play Again
              </button>
              <Link
                href="/games"
                className="flex-1 bg-gradient-to-r from-slate-200 to-gray-300 text-slate-700 font-bold rounded-2xl px-8 py-4 text-center hover:from-slate-300 hover:to-gray-400 transition-all transform hover:scale-105 text-lg"
              >
                🏠 Back to Games
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Instructions */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
        <div className="bg-black/60 backdrop-blur-lg rounded-2xl px-6 py-3 text-white text-center shadow-2xl border border-white/20 max-w-sm">
          <div className="font-semibold text-sm md:text-base">
            <div className="hidden md:block">⬆️ ⬇️ Move Between Lanes • ➡️ Speed Boost</div>
            <div className="md:hidden">📱 Tap to Move • Double-tap for Boost</div>
          </div>
          {speedBoostActive && (
            <div className="text-yellow-400 text-sm mt-2 animate-pulse font-bold">
              ⚡ SPEED BOOST ACTIVE! ⚡
            </div>
          )}
          {streak > 0 && (
            <div className="text-orange-400 text-sm mt-1 font-bold">
              🔥 {streak} Streak! Keep it up!
            </div>
          )}
        </div>
      </div>
    </div>
  );
}