import { SupabaseClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';
import { EnhancedGameService, AssignmentAnalytics } from './enhancedGameService';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface AssignmentTemplate {
  id?: string;
  teacher_id: string;
  name: string;
  description?: string;
  game_type: string;
  default_config: Record<string, any>;
  vocabulary_list_id?: string;
  estimated_duration: number;
  difficulty_level: string;
  max_attempts: number;
  usage_count: number;
  is_public: boolean;
  tags: string[];
}

export interface EnhancedAssignmentProgress {
  id?: string;
  assignment_id: string;
  student_id: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue' | 'submitted';
  attempts_count: number;
  max_attempts: number;
  
  // Performance metrics
  best_score: number;
  best_accuracy: number;
  total_time_spent: number;
  average_session_time: number;
  
  // Learning analytics
  words_mastered: number;
  words_struggling: number;
  improvement_rate: number;
  consistency_score: number;
  
  // Timestamps
  first_attempt_at?: Date;
  last_attempt_at?: Date;
  completed_at?: Date;
  submitted_at?: Date;
  
  // Feedback
  teacher_feedback?: string;
  auto_feedback: Record<string, any>;
  student_reflection?: string;
  
  // Metadata
  session_ids: string[];
  progress_data: Record<string, any>;
}

export interface AssignmentCreationData {
  title: string;
  description?: string;
  game_type: string;
  class_id: string;
  due_date?: Date;
  vocabulary_list_id?: string;
  config: Record<string, any>;
  points: number;
  time_limit: number;
  max_attempts: number;
  auto_grade: boolean;
  feedback_enabled: boolean;
  hints_allowed: boolean;
  power_ups_enabled: boolean;
}

export interface ClassPerformanceMetrics {
  assignment_id: string;
  total_students: number;
  completion_rate: number;
  average_score: number;
  average_accuracy: number;
  average_time_spent: number;
  difficulty_rating: number;
  engagement_score: number;
  top_performers: StudentPerformance[];
  struggling_students: StudentPerformance[];
  word_difficulty_analysis: WordDifficultyData[];
}

export interface StudentPerformance {
  student_id: string;
  student_name: string;
  score: number;
  accuracy: number;
  time_spent: number;
  attempts: number;
  status: string;
  improvement_trend: 'improving' | 'stable' | 'declining';
}

export interface WordDifficultyData {
  word: string;
  translation: string;
  error_rate: number;
  average_response_time: number;
  students_struggling: number;
  common_mistakes: string[];
}

// =====================================================
// ENHANCED ASSIGNMENT SERVICE CLASS
// =====================================================

export class EnhancedAssignmentService {
  private supabase: SupabaseClient;
  private gameService: EnhancedGameService;
  
  constructor(supabaseClient?: SupabaseClient) {
    this.supabase = supabaseClient || createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    );
    this.gameService = new EnhancedGameService(this.supabase);
  }

  // =====================================================
  // ASSIGNMENT TEMPLATE MANAGEMENT
  // =====================================================

  async createAssignmentTemplate(template: Omit<AssignmentTemplate, 'id' | 'usage_count'>): Promise<string> {
    const { data, error } = await this.supabase
      .from('assignment_templates')
      .insert({
        ...template,
        usage_count: 0
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create assignment template: ${error.message}`);
    }

    return data.id;
  }

  async getAssignmentTemplates(teacherId: string, includePublic: boolean = true): Promise<AssignmentTemplate[]> {
    let query = this.supabase
      .from('assignment_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (includePublic) {
      query = query.or(`teacher_id.eq.${teacherId},is_public.eq.true`);
    } else {
      query = query.eq('teacher_id', teacherId);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get assignment templates: ${error.message}`);
    }

    return data || [];
  }

  async useTemplate(templateId: string): Promise<void> {
    const { error } = await this.supabase
      .from('assignment_templates')
      .update({ usage_count: this.supabase.sql`usage_count + 1` })
      .eq('id', templateId);

    if (error) {
      throw new Error(`Failed to update template usage: ${error.message}`);
    }
  }

  // =====================================================
  // ENHANCED ASSIGNMENT CREATION
  // =====================================================

  async createEnhancedAssignment(
    teacherId: string, 
    assignmentData: AssignmentCreationData
  ): Promise<string> {
    // Create the assignment
    const { data: assignment, error: assignmentError } = await this.supabase
      .from('assignments')
      .insert({
        title: assignmentData.title,
        description: assignmentData.description,
        type: assignmentData.game_type,
        class_id: assignmentData.class_id,
        due_date: assignmentData.due_date?.toISOString(),
        vocabulary_assignment_list_id: assignmentData.vocabulary_list_id,
        created_by: teacherId,
        config: {
          ...assignmentData.config,
          max_attempts: assignmentData.max_attempts,
          auto_grade: assignmentData.auto_grade,
          feedback_enabled: assignmentData.feedback_enabled,
          hints_allowed: assignmentData.hints_allowed,
          power_ups_enabled: assignmentData.power_ups_enabled
        },
        points: assignmentData.points,
        time_limit: assignmentData.time_limit,
        status: 'active'
      })
      .select('id')
      .single();

    if (assignmentError) {
      throw new Error(`Failed to create assignment: ${assignmentError.message}`);
    }

    // Initialize assignment analytics
    await this.initializeAssignmentAnalytics(assignment.id);

    // Create progress entries for all students in the class
    await this.initializeStudentProgress(assignment.id, assignmentData.class_id);

    return assignment.id;
  }

  private async initializeAssignmentAnalytics(assignmentId: string): Promise<void> {
    const { error } = await this.supabase
      .from('assignment_analytics')
      .insert({
        assignment_id: assignmentId,
        total_students: 0,
        students_started: 0,
        students_completed: 0,
        completion_rate: 0,
        average_score: 0,
        average_accuracy: 0,
        average_time_spent: 0,
        difficulty_rating: 0,
        words_causing_difficulty: [],
        common_mistakes: {},
        average_attempts: 0,
        dropout_rate: 0,
        help_requests: 0,
        peak_activity_hours: [],
        average_session_length: 0
      });

    if (error) {
      throw new Error(`Failed to initialize assignment analytics: ${error.message}`);
    }
  }

  private async initializeStudentProgress(assignmentId: string, classId: string): Promise<void> {
    // Get all students in the class
    const { data: students, error: studentsError } = await this.supabase
      .from('class_memberships')
      .select('student_id')
      .eq('class_id', classId);

    if (studentsError) {
      throw new Error(`Failed to get class students: ${studentsError.message}`);
    }

    if (!students || students.length === 0) {
      return;
    }

    // Create progress entries for all students
    const progressEntries = students.map(student => ({
      assignment_id: assignmentId,
      student_id: student.student_id,
      status: 'not_started' as const,
      attempts_count: 0,
      max_attempts: 3,
      best_score: 0,
      best_accuracy: 0,
      total_time_spent: 0,
      average_session_time: 0,
      words_mastered: 0,
      words_struggling: 0,
      improvement_rate: 0,
      consistency_score: 0,
      auto_feedback: {},
      session_ids: [],
      progress_data: {}
    }));

    const { error } = await this.supabase
      .from('enhanced_assignment_progress')
      .insert(progressEntries);

    if (error) {
      throw new Error(`Failed to initialize student progress: ${error.message}`);
    }

    // Update total students count in analytics
    await this.supabase
      .from('assignment_analytics')
      .update({ total_students: students.length })
      .eq('assignment_id', assignmentId);
  }

  // =====================================================
  // ASSIGNMENT PROGRESS TRACKING
  // =====================================================

  async updateAssignmentProgress(
    assignmentId: string,
    studentId: string,
    sessionId: string,
    sessionData: any
  ): Promise<void> {
    // Get current progress
    const { data: progress, error: progressError } = await this.supabase
      .from('enhanced_assignment_progress')
      .select('*')
      .eq('assignment_id', assignmentId)
      .eq('student_id', studentId)
      .single();

    if (progressError) {
      throw new Error(`Failed to get assignment progress: ${progressError.message}`);
    }

    // Calculate updates
    const updates: Partial<EnhancedAssignmentProgress> = {
      attempts_count: progress.attempts_count + 1,
      last_attempt_at: new Date(),
      total_time_spent: progress.total_time_spent + (sessionData.duration_seconds || 0),
      session_ids: [...progress.session_ids, sessionId]
    };

    // Update best scores if improved
    if (sessionData.final_score > progress.best_score) {
      updates.best_score = sessionData.final_score;
    }

    if (sessionData.accuracy_percentage > progress.best_accuracy) {
      updates.best_accuracy = sessionData.accuracy_percentage;
    }

    // Update status
    if (progress.status === 'not_started') {
      updates.status = 'in_progress';
      updates.first_attempt_at = new Date();
    }

    // Check if assignment is completed (based on score threshold or completion percentage)
    if (sessionData.completion_percentage >= 80 || sessionData.accuracy_percentage >= 85) {
      updates.status = 'completed';
      updates.completed_at = new Date();
    }

    // Calculate learning metrics
    updates.words_mastered = sessionData.words_correct || 0;
    updates.words_struggling = (sessionData.words_attempted || 0) - (sessionData.words_correct || 0);
    
    // Calculate improvement rate
    if (progress.attempts_count > 0) {
      const previousAccuracy = progress.best_accuracy;
      const currentAccuracy = sessionData.accuracy_percentage;
      updates.improvement_rate = ((currentAccuracy - previousAccuracy) / previousAccuracy) * 100;
    }

    // Update average session time
    updates.average_session_time = Math.round(updates.total_time_spent / updates.attempts_count);

    // Generate auto feedback
    updates.auto_feedback = this.generateAutoFeedback(sessionData, progress);

    // Update progress
    const { error: updateError } = await this.supabase
      .from('enhanced_assignment_progress')
      .update(updates)
      .eq('assignment_id', assignmentId)
      .eq('student_id', studentId);

    if (updateError) {
      throw new Error(`Failed to update assignment progress: ${updateError.message}`);
    }

    // Update assignment analytics
    await this.updateAssignmentAnalytics(assignmentId);
  }
